# File Structure and Navigation SOP

* **Document ID:** SOP-FSA-002
* **Version:** v1.0
* **Effective Date:** 2025-07-01
* **Last Updated:** 2025-06-15
* **Author(s):** [Your Company Name]
* **Approved By:** [ORGANIZATION_NAME] Leadership/Administrator

---

## 1. Purpose

The purpose of this Standard Operating Procedure (SOP) is to provide clear guidance on navigating and utilizing [ORGANIZATION_NAME]'s standardized file structure. This SOP ensures all staff and volunteers can efficiently locate, store, and manage digital files within the centralized Network Attached Storage (NAS) system.

## 2. Scope

This SOP applies to all staff and authorized volunteers of [ORGANIZATION_NAME] who access, create, or manage digital files within the organization's file management system. This includes understanding the hierarchical folder structure and knowing where to store different types of documents.

## 3. Definitions

* **SOP:** Standard Operating Procedure.
* **NAS:** Network Attached Storage; the primary location for shared digital files.
* **Hierarchy:** A system of organizing data in a tree-like structure, with parent folders and sub-folders.
* **Root Directory:** The main, top-level folder of the file system (e.g., `[ORGANIZATION_NAME]_FILES/`).

## 4. Procedure: Understanding and Using the File Structure

The file structure is designed to follow NIST guidelines for electronic file organization, tailored specifically for [ORGANIZATION_NAME]'s functions. It emphasizes clear instructions, follows a numbered, hierarchical system designed for intuitive navigation and logical organization, consistent naming, and ease of navigation.

### 4.1. Main Directory Structure

All church-related files will reside under a single root directory, named `[ORGANIZATION_NAME]_FILES/`. This root directory contains 12 main, numbered categories to ensure logical order and quick identification:

```ts
[ORGANIZATION_NAME]_FILES/
├── 01_LITURGY_AND_WORSHIP/
├── 02_ADMINISTRATION/
├── 03_COMMUNICATIONS/
├── 04_MINISTRIES/
├── 05_FACILITIES/
├── 06_EVENTS/
├── 07_RESOURCES/
├── 08_FINANCE/
├── 09_EDUCATION/
├── 10_OUTREACH/
├── 11_POWERCHURCH_EXPORTS/
└── 12_ARCHIVE/
```

**Do Not Create New Top-Level Folders:** Users are strictly prohibited from creating new numbered top-level directories within the `[ORGANIZATION_NAME]_FILES/` root. All new major categories must be approved by the IT Administrator/System Manager.

### 4.2.1 Detailed Directory Descriptions and Usage

Each main directory is designed for specific types of content. Below is a detailed breakdown of each main directory and examples of its typical subdirectories. Use these as a guide to determine where to save and retrieve files.

#### 4.2.1 `01_LITURGY_AND_WORSHIP/`

* **Purpose:** Contains all materials related to liturgical services, worship planning, and sacramental life.
* **Examples:**
  * `01_Sunday_Services/` (e.g., `2025-05_Services/` for bulletins, readings)
  * `02_Music/` (e.g., `Hymn_Sheets/`, `Choir_Rehearsals/`)
  * `03_Sacraments/` (e.g., `Baptism_Prep_Materials/`, `Wedding_Guidelines/`)
  * `04_Special_Services/` (e.g., `Easter_Planning/`, `Christmas_Vespers/`)

#### 4.2.2. `02_ADMINISTRATION/`

* **Purpose:** Stores documents related to the general management and governance of [ORGANIZATION_NAME].
* **Examples:**
  * `01_Policies_and_Procedures/`
  * `02_Board_and_Council_Meetings/` (e.g., `2025_Vestry_Minutes/`)
  * `03_Human_Resources/` (e.g., `Employee_Handbooks/`, `Volunteer_Agreements/`)
  * `04_Legal_Documents/` (e.g., `Deeds/`, `Bylaws/`)
  * `05_Reports_and_Statistics/` (Non-PowerChurch related)

#### 4.2.3. `03_COMMUNICATIONS/`

* **Purpose:** Houses all materials related to internal and external communications, media, and branding.
* **Examples:**
  * `01_Newsletters/`
  * `02_Website_Content/`
  * `03_Social_Media/`
  * `04_Press_Releases/`
  * `05_Branding_Assets/` (e.g., `Logos/`, `Templates/`)

#### 4.2.4. `04_MINISTRIES/`

* **Purpose:** Dedicated folders for each active ministry, containing their specific operational documents.
* **Examples:**
  * `01_Youth_Ministry/` (e.g., `Curriculum/`, `Event_Planning/`)
  * `02_Adult_Faith_Formation/`
  * `03_Pastoral_Care/`
  * `04_Liturgical_Ministers/`

#### 4.2.5. `05_FACILITIES/`

* **Purpose:** Contains documents related to the maintenance, repair, and management of [ORGANIZATION_NAME]'s physical property.
* **Examples:**
  * `01_Maintenance_Logs/`
  * `02_Building_Plans/`
  * `03_Contracts_and_Vendors/`
  * `04_Safety_and_Security/`

#### 4.2.6. `06_EVENTS/`

* **Purpose:** For planning and documentation of specific church events (excluding regular services).
* **Examples:**
  * `01_Annual_Picnic_2025/`
  * `02_Fundraising_Gala_2025/`
  * `03_Concerts_and_Performances/`

#### 4.2.7. `07_RESOURCES/`

* **Purpose:** A general repository for shared resources, templates, and reference materials.
* **Examples:**
  * `01_Forms_and_Templates/`
  * `02_Sermon_Resources/`
  * `03_External_Research_Documents/`

#### 4.2.8. `08_FINANCE/`

* **Purpose:** Documents related to financial operations not directly managed by PowerChurch, such as budgets, audits, and investment information.
* **Examples:**
  * `01_Budgets/` (e.g., `2025_Budget_FINAL/`)
  * `02_Audit_Documents/`
  * `03_Grants_and_Donations_External/`
  * `04_Insurance_Documents/`
  * `05_Payroll_Documents/` (Non-PowerChurch related)

#### 4.2.9. `09_EDUCATION/`

* **Purpose:** Materials for educational programs, including Sunday School, adult education, and vocational training.
* **Examples:**
  * `01_Sunday_School_Curriculum/`
  * `02_Adult_Education_Classes/`
  * `03_VBS_Materials/`

#### 4.2.10. `10_OUTREACH/`

* **Purpose:** Documents related to community outreach programs, mission trips, and charitable activities.
* **Examples:**
  * `01_Community_Programs/`
  * `02_Mission_Trip_Planning/`
  * `03_Partnerships_and_Collaborations/`

#### 4.2.11. `11_POWERCHURCH_EXPORTS/`

* **Purpose:** Dedicated to storing reports and data exported from PowerChurch. This ensures these critical exports are consistently located and managed.
* **Examples:**
  * `01_Financial_Reports/` (e.g., `Monthly_Reports/`, `Quarterly_Reports/`, `Annual_Reports/`)
  * `02_Membership_Reports/` (e.g., `Directory_Exports/`, `Attendance_Reports/`)
  * `03_Contribution_Reports/` (e.g., `Giving_Statements/`, `Pledge_Reports/`)
  * `04_Backup_Files/` (Specific PowerChurch backup files, if applicable)

#### 4.2.12. `12_ARCHIVE/`

* **Purpose:** Contains older, inactive, or historical documents that are no longer actively used but must be retained according to retention policies. Files are moved here, not deleted.
* **Structure:** Organized by year, mirroring the active structure within each year.
* **Example:**

    ```ts
    12_ARCHIVE/
    ├── 2024/
    │   ├── 01_LITURGY_AND_WORSHIP/
    │   ├── 02_ADMINISTRATION/
    │   └── ... (mirrors active structure for that year)
    └── [YYYY]/
    ```

### 4.3. If in Doubt, Ask

If you are unsure where a file belongs, or cannot find a suitable existing folder:

1. Review this SOP again and the File Naming Conventions SOP.
2. Check the "Temporary" or "In Progress" folder within your immediate department/ministry folder if one exists (to be defined by department).
3. Contact your Department/Ministry Lead or the IT Administrator/System Manager for guidance. Do not create new top-level directories without approval.

---

## 5. Roles & Responsibilities

* **All Staff & Volunteers:** Responsible for saving files to the correct locations and navigating the file structure efficiently.
* **Department/Ministry Leads:** Responsible for guiding their teams on proper file placement and ensuring adherence within their specific areas.
* **IT Administrator / System Manager:** Responsible for establishing and maintaining the overall directory structure, managing permissions, and approving any new top-level categories.

## 6. Related Documents

* SOP-FNC-001: File Naming Conventions Guide
* SOP-NAS-003: NAS Usage & Best Practices
* SOP-DRAD-004: Data Retention, Archiving, and Deletion Policy

## 7. Revision History

| Version | Date         | Description       |
| :------ | :----------- | :---------------- |
| v1.0    | 2025-06-15   | Initial Release.  |
