# NAS Usage and Best Practices SOP

* **Document ID:** SOP-NAS-003
* **Version:** v1.0
* **Effective Date:** 2025-07-01
* **Last Updated:** 2025-06-16
* **Author(s):** [Your Company Name]
* **Approved By:** [ORGANIZATION_NAME] Leadership/Administrator

---

## 1. Purpose

The purpose of this Standard Operating Procedure (SOP) is to provide guidelines for the effective and secure use of [ORGANIZATION_NAME]'s Network Attached Storage (NAS) system. Adherence to these practices ensures centralized, reliable, and accessible storage for all digital files, protects data integrity, and optimizes system performance.

## 2. Scope

This SOP applies to all staff and authorized volunteers of [ORGANIZATION_NAME] who access, store, or manage digital files on the designated central NAS. This includes interaction with shared drives mounted from the NAS on individual workstations.

## 3. Definitions

* **SOP:** Standard Operating Procedure.
* **NAS:** Network Attached Storage; a dedicated file server connected to the network that provides centralized data storage and access. At [ORGANIZATION_NAME], this is a **QNAP TS-453A** device.
* **RAID 5:** A storage configuration used on the NAS that combines four (4) drives to provide data redundancy, allowing the system to continue operating even if one drive fails. It offers approximately 12 TB of usable storage.
* **Shared Drive:** A network drive (folder) on the NAS that is accessible by multiple users.
* **Snapshots:** Point-in-time "photos" of the data on the NAS, used for quick recovery of accidentally deleted or modified files.

## 4. Procedure: Using the NAS Effectively

### 4.1. Accessing the NAS Shared Drive

1.  **Connection:** The NAS shared drive(s) will be mapped as network drives on all church computers. You should see it appear under "This PC" (Windows) or "Locations" (macOS) as a designated drive letter (e.g., `Z:` or `N:`).
2.  **Login Credentials:** Access to specific folders on the NAS is managed by user permissions. Log in with your assigned [ORGANIZATION_NAME] network credentials.
3.  **Troubleshooting Connection:** If you cannot access the shared drive, ensure your computer is connected to the church network (wired or Wi-Fi). If issues persist, contact the IT Administrator/System Manager.

### 4.2. Saving and Opening Files

1.  **Saving New Files:**
    * Always save new church-related files directly to the appropriate folder within the NAS shared drive, following the `File_Structure_and_Navigation_SOP.md`.
    * **Do not save active church-related files only to your local computer's hard drive** (e.g., "Documents," "Desktop") as these are not backed up by the central system and are not accessible to others.
2.  **Saving Changes:** When working on an existing file from the NAS, simply save your changes as you normally would. The file will be updated directly on the NAS.
3.  **File Naming:** Always adhere strictly to the `File_Naming_Convention_SOP.md` when saving any file to the NAS.
4.  **Opening Files:** Navigate to the correct folder on the shared drive and double-click the file to open it.

### 4.3. Permissions and Access Control

1.  **Managed Access:** Access to specific folders on the NAS is controlled by permissions set by the IT Administrator/System Manager. You will only see and be able to access folders relevant to your role and ministry.
2.  **Requesting Access:** If you require access to a folder you cannot open, contact your Department/Ministry Lead, who will then forward the request to the IT Administrator/System Manager.
3.  **Do Not Share Passwords:** Never share your network login credentials.

### 4.4. Managing Files on the NAS

1.  **Creating Folders:** You may create new sub-folders *within* your designated ministry or department directories as needed, ensuring they follow logical naming. **Do not create new top-level numbered folders** without IT Administrator approval.
2.  **Deleting Files:**
    * You may delete your own draft files that are no longer needed.
    * **Never delete a shared "FINAL" document** without explicit approval from your Department/Ministry Lead and/or the IT Administrator/System Manager.
    * Refer to `Data_Retention_and_Archiving_SOP.md` for guidelines on when files can be deleted or archived.
3.  **Moving Files:**
    * When moving files within the NAS, simply drag and drop them to the new location.
    * If moving a significant number of files or an entire folder to a new major category, consult with the IT Administrator/System Manager.

### 4.5. Data Protection and Recovery (User Awareness)

1.  **RAID 5 Protection:** The NAS is configured with RAID 5 (using 4 x 4TB WD Red drives), which protects data against a single drive failure. This means your data is safer than on a single local hard drive.
2.  **Snapshots:** The NAS system automatically takes regular snapshots of the data. If you accidentally delete or overwrite a file, the IT Administrator/System Manager can often recover an older version using these snapshots.
    * **To Request a Restore:** Contact the IT Administrator/System Manager immediately, providing the file name, approximate location, and the date/time the file was last known to be correct.
3.  **Backups:** In addition to RAID and snapshots, the NAS is regularly backed up to external locations. This is managed by the IT Administrator/System Manager and ensures data recovery in case of a major system failure.

### 4.6. Prohibited Use

1.  **Personal Files:** Do not store personal files (e.g., personal photos, music, non-church documents) on the [ORGANIZATION_NAME] NAS. This resource is for official church business only.
2.  **Illegal Content:** Storage, sharing, or accessing of any illegal content is strictly prohibited.
3.  **Software Installation:** Do not attempt to install software or make system changes on the NAS device directly. This is strictly for the IT Administrator/System Manager.

---

## 5. Roles & Responsibilities

* **All Staff & Volunteers:** Responsible for adhering to all NAS usage guidelines, understanding permission levels, and following file management procedures.
* **Department/Ministry Leads:** Responsible for ensuring their teams comply with this SOP and for approving file deletions or moves within their area.
* **IT Administrator / System Manager:** Responsible for NAS setup, maintenance, security, permissions management, data recovery, and updating this SOP.

## 6. Related Documents

* SOP-FNC-001: File Naming Conventions Guide
* SOP-FSA-002: File Structure & Navigation Guide
* SOP-DRAD-004: Data Retention, Archiving, and Deletion Policy
* [SOP-DBR-005]: Data Backup & Recovery Procedure (for IT/Admin) - *To be created*

## 7. Revision History

| Version | Date         | Description       |
| :------ | :----------- | :---------------- |
| v1.0    | 2025-06-16   | Initial Release. Includes QNAP TS-453A details. |