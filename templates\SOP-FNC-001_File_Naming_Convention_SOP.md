# File Naming Convention SOP

* **Document ID:** SOP-FNC-001
* **Version:** v1.0
* **Effective Date:** 2025-07-01
* **Last Updated:** 2025-06-16
* **Author(s):** [Your Company Name]
* **Approved By:** [ORGANIZATION_NAME] Leadership/Administrator

---

## 1. Purpose

The purpose of this Standard Operating Procedure (SOP) is to establish consistent, clear, and logical file naming conventions for all digital documents stored within [ORGANIZATION_NAME]'s centralized file management system. Proper file naming enhances discoverability, reduces confusion, and supports efficient collaboration among staff and volunteers.

## 2. Scope

This SOP applies to all staff and authorized volunteers of [ORGANIZATION_NAME] who create, save, or manage digital files within the organization's Network Attached Storage (NAS) system. This includes all document types: text documents, spreadsheets, presentations, images, audio files, and any other digital assets.

## 3. Definitions

* **SOP:** Standard Operating Procedure.
* **File Name:** The name assigned to a digital file, excluding the file extension (e.g., `.docx`, `.pdf`, `.xlsx`).
* **File Extension:** The suffix that indicates the file type (e.g., `.docx` for Word documents, `.pdf` for PDF files).
* **Version Control:** A system for tracking changes and iterations of a document over time.

## 4. File Naming Convention Rules

### 4.1. Standard Format

All files must follow this naming convention:

**`YYYY-MM-DD_Topic_DocumentType_Version`**

**Components:**
1. **Date (YYYY-MM-DD):** The date the document was created or the date it pertains to, in ISO 8601 format.
2. **Topic:** A brief, descriptive identifier of the document's subject matter.
3. **DocumentType:** The category or type of document.
4. **Version:** Version identifier for tracking document iterations.

**Examples:**
- `2025-06-16_Budget_Report_v1.0.xlsx`
- `2025-07-04_Independence_Service_Bulletin_v2.1.pdf`
- `2025-05-20_Youth_Meeting_Minutes_v1.0.docx`

### 4.2. Detailed Guidelines

#### 4.2.1. Date Component (YYYY-MM-DD)
- **Always use the ISO 8601 date format:** Four-digit year, two-digit month, two-digit day, separated by hyphens.
- **For event-related documents:** Use the date of the event.
- **For meeting documents:** Use the date of the meeting.
- **For reports:** Use the date the report covers or the date it was created.
- **For ongoing documents:** Use the date of creation or last major revision.

#### 4.2.2. Topic Component
- **Keep it concise:** 1-3 words maximum.
- **Use descriptive terms:** Choose words that clearly identify the subject.
- **Use title case:** Capitalize the first letter of each word.
- **Avoid special characters:** Use only letters, numbers, and underscores.
- **Examples:** `Budget`, `Youth_Ministry`, `Easter_Service`, `Board_Meeting`

#### 4.2.3. DocumentType Component
- **Use standardized terms:** Choose from the approved list below.
- **Be specific:** Select the most accurate document type.
- **Use title case:** Capitalize the first letter of each word.

**Approved Document Types:**
- `Minutes` - Meeting minutes
- `Agenda` - Meeting agendas
- `Report` - Reports and summaries
- `Budget` - Financial budgets and planning documents
- `Bulletin` - Service bulletins and announcements
- `Newsletter` - Newsletters and publications
- `Policy` - Policies and procedures
- `Contract` - Contracts and agreements
- `Invoice` - Invoices and billing documents
- `Receipt` - Receipts and payment records
- `Presentation` - PowerPoint presentations and slides
- `Flyer` - Promotional materials and flyers
- `Form` - Forms and templates
- `Letter` - Correspondence and letters
- `Sermon` - Sermon texts and notes
- `Liturgy` - Liturgical materials and orders of service
- `Music` - Musical scores and arrangements
- `Photo` - Photographs and images
- `Video` - Video files and recordings
- `Audio` - Audio files and recordings

#### 4.2.4. Version Component
- **Use semantic versioning:** `v[Major].[Minor]` format.
- **Major version (first number):** Increment for significant changes or complete rewrites.
- **Minor version (second number):** Increment for minor edits, corrections, or additions.
- **Start with v1.0:** All new documents begin with version 1.0.
- **Examples:** `v1.0`, `v1.1`, `v2.0`, `v2.3`

### 4.3. Special Cases and Exceptions

#### 4.3.1. Series Documents
For documents that are part of a regular series (e.g., weekly bulletins, monthly newsletters):
- **Include series identifier in Topic:** `Weekly_Bulletin`, `Monthly_Newsletter`
- **Use the date of the publication/event**

**Examples:**
- `2025-06-15_Weekly_Bulletin_v1.0.pdf`
- `2025-06-01_Monthly_Newsletter_v2.0.pdf`

#### 4.3.2. Multi-Part Documents
For documents that are split into multiple parts:
- **Add part identifier:** `_Part1`, `_Part2`, etc.
- **Place before version:** `Topic_DocumentType_Part1_Version`

**Example:**
- `2025-06-16_Annual_Report_Part1_v1.0.pdf`
- `2025-06-16_Annual_Report_Part2_v1.0.pdf`

#### 4.3.3. Draft Documents
For documents still in development:
- **Use "Draft" as DocumentType:** `_Draft_`
- **Remove "Draft" when finalized**

**Example:**
- `2025-06-16_Budget_Draft_v1.0.xlsx` (working version)
- `2025-06-16_Budget_Report_v1.0.xlsx` (final version)

## 5. Prohibited Practices

### 5.1. Characters to Avoid
- **Special characters:** `/ \ : * ? " < > |`
- **Spaces:** Use underscores instead
- **Leading/trailing spaces or periods**
- **Non-English characters** (unless specifically required)

### 5.2. Naming Practices to Avoid
- **Vague names:** `Document1.docx`, `Untitled.pdf`
- **Personal references:** `Johns_Notes.docx`, `Marys_File.xlsx`
- **Inconsistent capitalization:** `budget_REPORT_v1.0.xlsx`
- **Overly long names:** Keep total filename under 100 characters

## 6. Version Control Guidelines

### 6.1. When to Create a New Version
- **Minor edits:** Increment minor version (e.g., v1.0 → v1.1)
- **Significant changes:** Increment major version (e.g., v1.5 → v2.0)
- **Annual updates:** Typically warrant a new major version

### 6.2. Managing Previous Versions
- **Keep previous versions:** Move to appropriate archive folder
- **Clear labeling:** Ensure version numbers are accurate
- **Regular cleanup:** Archive old versions according to retention policy

## 7. Examples by Category

### 7.1. Administrative Documents
- `2025-06-16_Board_Minutes_v1.0.docx`
- `2025-07-01_Staff_Policy_v2.0.pdf`
- `2025-06-15_Annual_Budget_v1.0.xlsx`

### 7.2. Worship and Liturgy
- `2025-06-22_Sunday_Bulletin_v1.0.pdf`
- `2025-12-25_Christmas_Liturgy_v1.0.docx`
- `2025-06-15_Hymn_Selection_v1.0.xlsx`

### 7.3. Communications
- `2025-06-01_Monthly_Newsletter_v1.0.pdf`
- `2025-07-04_Independence_Flyer_v1.0.pdf`
- `2025-06-16_Website_Update_v1.0.docx`

### 7.4. Events and Programs
- `2025-08-15_Picnic_Planning_v1.0.docx`
- `2025-06-20_Youth_Event_Flyer_v1.0.pdf`
- `2025-07-01_VBS_Registration_Form_v1.0.pdf`

## 8. Roles & Responsibilities

* **All Staff & Volunteers:** Responsible for following this naming convention for all files they create or modify.
* **Department/Ministry Leads:** Responsible for ensuring their teams understand and comply with this SOP.
* **IT Administrator / System Manager:** Responsible for monitoring compliance, providing training, and updating this SOP as needed.

## 9. Related Documents

* SOP-FSA-002: File Structure & Navigation Guide
* SOP-NAS-003: NAS Usage and Best Practices
* SOP-DRAD-004: Data Retention, Archiving, and Deletion Policy

## 10. Revision History

| Version | Date         | Description       |
| :------ | :----------- | :---------------- |
| v1.0    | 2025-06-16   | Initial Release   |
