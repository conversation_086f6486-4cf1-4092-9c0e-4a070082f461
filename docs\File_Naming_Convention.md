# File Naming Conventions

1. **Date format**: YYYY-MM-DD (e.g., 2025-05-02)
   - For recurring documents: YYYY-MM-DD_DocumentType (e.g., 2025-05-15_VestryMinutes)

2. **Version control**:
   - DocumentName_v01, DocumentName_v02 (e.g., StrategicPlan_v03)
   - For final versions: DocumentName_FINAL

3. **Naming elements order**:
   - Date_Topic_DocumentType_Version
   - Example: 2025-05-15_Budget_Report_FINAL

4. **Characters to avoid**:
   - No spaces (use underscores)
   - No special characters: \ / : * ? " < > | # % & { } $ ! ' @ + = `

5. **Case consistency**:
   - Use snake_case for better readability
   - Example: strategic_plan_2025_draft

## Integration with PowerChurch

1. **Data Flow Management**:
   - Export reports from PowerChurch on a regular schedule
   - Store exports in the PowerChurch_Exports directory with consistent naming
   - Cross-reference PowerChurch data in relevant files without duplication

2. **References to PowerChurch Data**:
   - When creating documents that reference PowerChurch data, use references like:
     "See PowerChurch [Module] report: [Report Name] from [Date]"
   - Example: "See PowerChurch Membership report: New Members Q2 from 2025-06-30"

3. **PowerChurch Data vs. External Files**:
   - Use PowerChurch as the system of record for:
     - Membership data
     - Financial transactions
     - Contribution records
     - Calendar/scheduling data
   - Use file system for:
     - Document drafts and final versions
     - Supporting documentation
     - Media files
     - Resources and templates

## Implementation Plan

1. **Phase 1: Framework Creation**
   - Create the main directory structure
   - Establish naming conventions
   - Document the system

2. **Phase 2: Current Files Migration**
   - Identify and organize mission-critical files first
   - Move files to appropriate locations
   - Update references as needed

3. **Phase 3: Training and Adoption**
   - Train staff on the new system
   - Create quick reference guides
   - Establish ongoing maintenance procedures

4. **Phase 4: Review and Refinement**
   - Gather feedback after initial implementation
   - Make adjustments as needed
   - Conduct regular reviews of system effectiveness
